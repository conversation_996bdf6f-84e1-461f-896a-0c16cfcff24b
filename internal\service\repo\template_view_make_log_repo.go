package repo

import (
	"chongli/internal/service/dto"
	"context"
	"gorm.io/gorm"
)

type TemplateViewMakeLogRepo interface {
	Create(ctx context.Context, log *dto.TemplateViewMakeLogDTO, tx ...*gorm.DB) error
	Update(ctx context.Context, id int64, updates map[string]any, tx ...*gorm.DB) error
	Delete(ctx context.Context, id int64, tx ...*gorm.DB) error

	GetOne(ctx context.Context, query *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) (*dto.TemplateViewMakeLogDTO, error)
	List(ctx context.Context, query *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateViewMakeLogDTO, error)
	Page(ctx context.Context, query *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateViewMakeLogDTO, int64, error)
	Count(ctx context.Context, query *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) (int64, error)

	// Stats 统计指定模板在给定时间范围内的 浏览/制作 数量
	Stats(ctx context.Context, templateID int64, beginAt, endAt int64, tx ...*gorm.DB) (viewCount, makeCount int64, err error)
}
