package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"context"
	"errors"
	"gorm.io/gorm"
)

// templateViewMakeLogRepoImpl 实现 TemplateViewMakeLogRepo
// create_at 使用 Unix 秒时间戳，落库/查询时按 int64 处理

type templateViewMakeLogRepoImpl struct {
	db *gorm.DB
}

func NewTemplateViewMakeLogRepo(bootStrap *component.BootStrap) repo.TemplateViewMakeLogRepo {
	return &templateViewMakeLogRepoImpl{db: bootStrap.Driver.GetMysqlDb()}
}

func (r *templateViewMakeLogRepoImpl) getDB(tx ...*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return r.db
}

func (r *templateViewMakeLogRepoImpl) Create(_ context.Context, log *dto.TemplateViewMakeLogDTO, tx ...*gorm.DB) error {
	if log == nil {
		return errors.New("log is nil")
	}
	m := &model.TemplateViewMakeLog{
		ID:         log.ID,
		TemplateID: log.TemplateID,
		Type:       log.Type,
		CreateAt:   log.CreateAt,
	}
	return r.getDB(tx...).Create(m).Error
}

func (r *templateViewMakeLogRepoImpl) Update(_ context.Context, id int64, updates map[string]any, tx ...*gorm.DB) error {
	return r.getDB(tx...).Model(&model.TemplateViewMakeLog{}).Where("id = ?", id).Updates(updates).Error
}

func (r *templateViewMakeLogRepoImpl) Delete(_ context.Context, id int64, tx ...*gorm.DB) error {
	return r.getDB(tx...).Where("id = ?", id).Delete(&model.TemplateViewMakeLog{}).Error
}

func (r *templateViewMakeLogRepoImpl) buildQuery(db *gorm.DB, q *dto.TemplateViewMakeLogQueryDTO) *gorm.DB {
	if q == nil {
		return db
	}
	if q.ID > 0 {
		db = db.Where("id = ?", q.ID)
	}
	if q.TemplateID > 0 {
		db = db.Where("template_id = ?", q.TemplateID)
	}
	if q.Type > 0 {
		db = db.Where("type = ?", q.Type)
	}
	if q.BeginAt > 0 {
		db = db.Where("create_at >= ?", q.BeginAt)
	}
	if q.EndAt > 0 {
		db = db.Where("create_at <= ?", q.EndAt)
	}
	return db
}

func (r *templateViewMakeLogRepoImpl) GetOne(_ context.Context, q *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) (*dto.TemplateViewMakeLogDTO, error) {
	db := r.getDB(tx...)
	db = r.buildQuery(db, q)
	var m model.TemplateViewMakeLog
	if err := db.First(&m).Error; err != nil {
		return nil, err
	}
	return &dto.TemplateViewMakeLogDTO{
		ID:         m.ID,
		TemplateID: m.TemplateID,
		Type:       m.Type,
		CreateAt:   m.CreateAt,
	}, nil
}

func (r *templateViewMakeLogRepoImpl) List(_ context.Context, q *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateViewMakeLogDTO, error) {
	db := r.getDB(tx...)
	db = r.buildQuery(db, q)
	db = db.Order("id desc")
	var list []model.TemplateViewMakeLog
	if err := db.Find(&list).Error; err != nil {
		return nil, err
	}
	res := make([]*dto.TemplateViewMakeLogDTO, 0, len(list))
	for _, m := range list {
		item := m
		res = append(res, &dto.TemplateViewMakeLogDTO{
			ID:         item.ID,
			TemplateID: item.TemplateID,
			Type:       item.Type,
			CreateAt:   item.CreateAt,
		})
	}
	return res, nil
}

func (r *templateViewMakeLogRepoImpl) Page(_ context.Context, q *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateViewMakeLogDTO, int64, error) {
	db := r.getDB(tx...)
	db = r.buildQuery(db, q)

	var total int64
	if err := db.Model(&model.TemplateViewMakeLog{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if q.PageNum <= 0 {
		q.PageNum = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
	if q.PageSize > 1000 {
		q.PageSize = 1000
	}

	offset := (q.PageNum - 1) * q.PageSize
	db = db.Order("id desc").Offset(offset).Limit(q.PageSize)

	var list []model.TemplateViewMakeLog
	if err := db.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	res := make([]*dto.TemplateViewMakeLogDTO, 0, len(list))
	for _, m := range list {
		item := m
		res = append(res, &dto.TemplateViewMakeLogDTO{
			ID:         item.ID,
			TemplateID: item.TemplateID,
			Type:       item.Type,
			CreateAt:   item.CreateAt,
		})
	}
	return res, total, nil
}

func (r *templateViewMakeLogRepoImpl) Count(_ context.Context, q *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) (int64, error) {
	db := r.getDB(tx...)
	db = r.buildQuery(db, q)
	var cnt int64
	if err := db.Model(&model.TemplateViewMakeLog{}).Count(&cnt).Error; err != nil {
		return 0, err
	}
	return cnt, nil
}

func (r *templateViewMakeLogRepoImpl) Stats(_ context.Context, templateID int64, beginAt, endAt int64, tx ...*gorm.DB) (viewCount, makeCount int64, err error) {
	db := r.getDB(tx...)

	// 视图统计
	if err = db.Model(&model.TemplateViewMakeLog{}).
		Where("template_id = ? AND type = ?", templateID, 1).
		Where("create_at >= ? AND create_at <= ?", beginAt, endAt).
		Count(&viewCount).Error; err != nil {
		return
	}

	// 制作统计
	if err = db.Model(&model.TemplateViewMakeLog{}).
		Where("template_id = ? AND type = ?", templateID, 2).
		Where("create_at >= ? AND create_at <= ?", beginAt, endAt).
		Count(&makeCount).Error; err != nil {
		return
	}
	return
}
